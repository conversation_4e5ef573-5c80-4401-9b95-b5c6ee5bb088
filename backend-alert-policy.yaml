displayName: "Backend API Down Alert"
documentation:
  content: "Backend API service is down or not responding properly"
  mimeType: "text/markdown"
conditions:
  - displayName: "Backend API Uptime Check Failed"
    conditionThreshold:
      filter: 'resource.type="uptime_url" AND metric.type="monitoring.googleapis.com/uptime_check/check_passed" AND resource.label.host="gold-braid-458901-v2.uc.r.appspot.com"'
      comparison: "COMPARISON_GREATER_THAN"
      thresholdValue: 0.1
      duration: "300s"
      aggregations:
        - alignmentPeriod: "60s"
          perSeriesAligner: "ALIGN_FRACTION_TRUE"
          crossSeriesReducer: "REDUCE_MEAN"
          groupByFields:
            - "resource.label.host"
combiner: "OR"
enabled: true
notificationChannels: []
