import { NextResponse } from 'next/server';
import { SyncResponse } from '@/types/property';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://gold-braid-458901-v2.uc.r.appspot.com';

export async function POST() {
  try {
    const response = await fetch(`${API_BASE_URL}/api/refresh-data`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend API error: ${response.status} ${response.statusText}`);
    }

    const data: SyncResponse = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in refresh data API route:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to refresh data',
        message: error instanceof Error ? error.message : 'Unknown error',
        syncedCount: 0
      },
      { status: 500 }
    );
  }
}
