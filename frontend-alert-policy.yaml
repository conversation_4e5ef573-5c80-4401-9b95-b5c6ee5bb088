displayName: "Frontend App Down Alert"
documentation:
  content: "Frontend application is down or not responding properly"
  mimeType: "text/markdown"
conditions:
  - displayName: "Frontend App Uptime Check Failed"
    conditionThreshold:
      filter: 'resource.type="uptime_url" AND metric.type="monitoring.googleapis.com/uptime_check/check_passed" AND resource.label.host="frontend-dot-gold-braid-458901-v2.uc.r.appspot.com"'
      comparison: "COMPARISON_LESS_THAN"
      thresholdValue: 0.5
      duration: "300s"
      aggregations:
        - alignmentPeriod: "60s"
          perSeriesAligner: "ALIGN_FRACTION_TRUE"
          crossSeriesReducer: "REDUCE_MEAN"
          groupByFields:
            - "resource.label.host"
combiner: "OR"
enabled: true
notificationChannels: []
