package main

import (
    "encoding/json"
    "fmt"
    "log"
    "math/rand"
    "net/http"
    "net/url"
    "os"
    "path/filepath"
    "strconv"
    "time"
    "strings"
    "propbolt/zestimate"
    "propbolt/details"
    "propbolt/config"
    "propbolt/utils"
    "propbolt/database"
    "propbolt/search"
)

/* internal notes
lsof -i :8080
kill -9
git add .
git commit -m ""
git push origin main
Production: GOOS=linux GOARCH=amd64 go build -o propbolt
Local: go build -o propbolt
Local: PORT=8080 ./propbolt
Local with proxies: PORT=8080 PROXY_URLS="http://sp0o8xf1er:<EMAIL>:10001,http://sp0o8xf1er:<EMAIL>:10002" ./propbolt
*/

// Global proxy configuration
var proxyConfig *config.ProxyConfig

// Global proxy rotator for better sharing across requests
var globalProxyRotator *utils.ProxyRotator

func init() {
    // Initialize random seed
    rand.Seed(time.Now().UnixNano())

    // Load proxy configuration
    var err error
    proxyConfig, err = config.LoadProxyConfig()
    if err != nil {
        log.Printf("Warning: Failed to load proxy configuration: %v", err)
    } else if proxyConfig.ProxyRotator.Count() > 0 {
        globalProxyRotator = proxyConfig.ProxyRotator
        log.Printf("Initialized with %d proxies", globalProxyRotator.Count())
    } else {
        log.Printf("Warning: No proxies configured")
    }

    // Initialize database connection
    if err := database.InitDB(); err != nil {
        log.Printf("Warning: Failed to initialize database: %v", err)
    }
}

// CORS middleware to handle cross-origin requests
func corsMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        // Get the origin from the request
        origin := r.Header.Get("Origin")

        // Define allowed origins
        allowedOrigins := []string{
            "http://localhost:3000",
            "http://localhost:8080",
            "https://propbolt.com",
            "https://www.propbolt.com",
            "https://api.propbolt.com",
            "https://frontend-dot-gold-braid-458901-v2.uc.r.appspot.com",
            "https://default-dot-gold-braid-458901-v2.uc.r.appspot.com",
        }

        // Check if origin is allowed
        originAllowed := false
        for _, allowedOrigin := range allowedOrigins {
            if origin == allowedOrigin {
                originAllowed = true
                break
            }
        }

        // Set CORS headers
        if originAllowed {
            w.Header().Set("Access-Control-Allow-Origin", origin)
        } else {
            w.Header().Set("Access-Control-Allow-Origin", "*") // Fallback for development
        }

        w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
        w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, x-api-key, x-user-id")
        w.Header().Set("Access-Control-Allow-Credentials", "true")
        w.Header().Set("Access-Control-Max-Age", "86400") // 24 hours

        // Handle preflight OPTIONS request
        if r.Method == "OPTIONS" {
            w.WriteHeader(http.StatusOK)
            return
        }

        next.ServeHTTP(w, r)
    })
}

// Middleware for basic request logging (RapidAPI authentication removed)
func requestLoggingMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        // Log the request for debugging purposes
        log.Printf("Request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

        // All endpoints are now publicly accessible
        next.ServeHTTP(w, r)
    })
}

// Handler for the health check endpoint
func healthCheckHandler(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(http.StatusOK)
    json.NewEncoder(w).Encode(map[string]string{"status": "ok"})
}

// Handler for serving static files
func serveStaticFile(filePath string) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        http.ServeFile(w, r, filepath.Join("public", filePath))
    }
}

// Handler for serving the favicon
func faviconHandler(w http.ResponseWriter, r *http.Request) {
    http.ServeFile(w, r, filepath.Join("public", "favicon.ico"))
}

// New handler for the /property endpoint
func propertyHandler(w http.ResponseWriter, r *http.Request) {
    // Parse the listingPhotos parameter
    listingPhotosStr := r.URL.Query().Get("listingPhotos")
    listingPhotos := false
    if listingPhotosStr == "true" {
        listingPhotos = true
    }

    var property details.PropertyInfo
    var err error

    // Get a proxy URL from the rotator
    var proxyURL *url.URL
    if proxyConfig != nil && proxyConfig.ProxyRotator.Count() > 0 {
        proxyURL = proxyConfig.ProxyRotator.GetNext()
        log.Printf("Using proxy for property request: %v", proxyURL)
    }

    // Check for ID parameter
    propertyIDStr := r.URL.Query().Get("id")
    if propertyIDStr != "" {
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }

        property, err = details.FromPropertyID(propertyID, proxyURL)
    } else {
        // Check for URL parameter
        propertyURL := r.URL.Query().Get("url")
        if propertyURL != "" {
            property, err = details.FromPropertyURL(propertyURL, proxyURL)
        } else {
            // Check for Address parameter
            homeAddress := r.URL.Query().Get("address")
            if homeAddress != "" {
                property, err = details.FromHomeAddress(homeAddress, proxyURL)
            } else {
                // If no valid parameters are provided, return an error
                http.Error(w, "Must provide ID, URL, or Address parameter", http.StatusBadRequest)
                return
            }
        }
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    if !listingPhotos {
        property.ResponsivePhotos = nil
    }

    writeJSONResponse(w, property)
}

func rentZestimateHandler(w http.ResponseWriter, r *http.Request) {
    address := r.URL.Query().Get("address")
    if address == "" {
        http.Error(w, "Address parameter is required", http.StatusBadRequest)
        return
    }

    var compPropStatus *bool
    compPropStatusStr := r.URL.Query().Get("compPropStatus")
    if compPropStatusStr != "" {
        if compPropStatusStr == "true" || compPropStatusStr == "false" {
            val, _ := strconv.ParseBool(compPropStatusStr)
            compPropStatus = &val
        } else {
            http.Error(w, "Invalid compPropStatus parameter", http.StatusBadRequest)
            return
        }
    }

    distanceInMilesStr := r.URL.Query().Get("distanceInMiles")
    var distanceInMiles float64 = 5 // Default value
    if distanceInMilesStr != "" {
        var err error
        distanceInMiles, err = strconv.ParseFloat(distanceInMilesStr, 64)
        if err != nil {
            http.Error(w, "Invalid distanceInMiles parameter", http.StatusBadRequest)
            return
        }
    }

    // Use the global proxy rotator with retry logic
    rentZestimate, err := zestimate.GetRentZestimateWithRotator(address, compPropStatus, distanceInMiles, globalProxyRotator)
    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving rent zestimate: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, rentZestimate)
}

func main() {
    // Test proxy connectivity at startup
    if globalProxyRotator != nil && globalProxyRotator.Count() > 0 {
        log.Printf("Testing proxy connectivity...")
        testProxyConnectivity()
    }
    
    mux := http.NewServeMux()

    mux.HandleFunc("/property", propertyHandler) // New unified endpoint
    mux.HandleFunc("/propertyMinimal", propertyMinimalHandler)
    mux.HandleFunc("/propertyImages", propertyImagesHandler)
    mux.HandleFunc("/rentEstimate", rentZestimateHandler) // New endpoint for property images
    mux.HandleFunc("/search", searchEndpointHandler) // Property search endpoint
    mux.HandleFunc("/status", statusHandler) // Status endpoint to check proxy health
    mux.HandleFunc("/test-proxy", testProxyHandler) // Test proxy connectivity

    // Health Check Endpoint
    mux.HandleFunc("/", healthCheckHandler)

    // Favicon Endpoint
    mux.HandleFunc("/favicon.ico", serveStaticFile("favicon.ico"))

    // Additional static file routes
    mux.HandleFunc("/api-logo.png", serveStaticFile("api-logo.png"))
    mux.HandleFunc("/cover-photo-tutorial-one.png", serveStaticFile("cover-photo-tutorial-one.png"))
    mux.HandleFunc("/rapid/tutorial-one-code", serveStaticFile("tutorials-one.html"))
    mux.HandleFunc("/byte-media-logo-v2.png", serveStaticFile("byte-media-logo-v2.png"))
    mux.HandleFunc("/rapid/curl", serveStaticFile("curl_input.html"))

    // Dashboard routes
    mux.HandleFunc("/dashboard", serveStaticFile("dashboard.html"))
    mux.HandleFunc("/admin", serveStaticFile("dashboard.html"))
    mux.HandleFunc("/admin/", serveStaticFile("dashboard.html"))

    // API routes for dashboard
    mux.HandleFunc("/api/search", searchHandler)
    mux.HandleFunc("/api/dashboard/stats", dashboardStatsHandler)
    mux.HandleFunc("/api/sync-properties", syncPropertiesHandler)
    mux.HandleFunc("/api/refresh-data", refreshDataHandler)

    // Serve static files from the 'public' directory without authentication
    mux.Handle("/public/", http.StripPrefix("/public/", http.FileServer(http.Dir("public"))))

    // Apply CORS middleware first, then request logging middleware
    handler := corsMiddleware(requestLoggingMiddleware(mux))

    port := os.Getenv("PORT")
    if port == "" {
        log.Fatal("PORT environment variable is not set")
    }

    fmt.Printf("Server started at port %s (no authentication required)\n", port)
    log.Fatal(http.ListenAndServe(":"+port, handler))
}

func propertyMinimalHandler(w http.ResponseWriter, r *http.Request) {
    // Parse the listingPhotos parameter
    listingPhotosStr := r.URL.Query().Get("listingPhotos")
    listingPhotos := false
    if listingPhotosStr == "true" {
        listingPhotos = true
    }

    // Get a proxy URL from the rotator
    var proxyURL *url.URL
    if proxyConfig != nil && proxyConfig.ProxyRotator.Count() > 0 {
        proxyURL = proxyConfig.ProxyRotator.GetNext()
        log.Printf("Using proxy for property minimal request: %v", proxyURL)
    }

    propertyIDStr := r.URL.Query().Get("id")
    propertyURLParam := r.URL.Query().Get("url")
    homeAddress := r.URL.Query().Get("address")

    var property details.PropertyMinimalInfo
    var err error

    if propertyIDStr != "" {
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }
        property, err = details.FromPropertyIDMinimal(propertyID, proxyURL)
    } else if propertyURLParam != "" {
        property, err = details.FromPropertyURLMinimal(propertyURLParam, proxyURL)
    } else if homeAddress != "" {
        property, err = details.FromHomeAddressMinimal(homeAddress, proxyURL)
    } else {
        http.Error(w, "Either Property ID, URL, or Address is required", http.StatusBadRequest)
        return
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    if !listingPhotos {
        property.ResponsivePhotos = nil
    }

    writeJSONResponse(w, property)
}

func propertyImagesHandler(w http.ResponseWriter, r *http.Request) {
    // Get a proxy URL from the rotator
    var proxyURL *url.URL
    if proxyConfig != nil && proxyConfig.ProxyRotator.Count() > 0 {
        proxyURL = proxyConfig.ProxyRotator.GetNext()
        log.Printf("Using proxy for property images request: %v", proxyURL)
    }

    propertyIDStr := r.URL.Query().Get("id")
    propertyURLParam := r.URL.Query().Get("url")
    homeAddress := r.URL.Query().Get("address")

    var property details.ImagesOnly
    var err error

    switch {
    case propertyIDStr != "":
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }
        property, err = details.FromPropertyIDPhotos(propertyID, proxyURL)
    case propertyURLParam != "":
        property, err = details.FromPropertyURLPhotos(propertyURLParam, proxyURL)
    case homeAddress != "":
        property, err = details.FromHomeAddressPhotos(homeAddress, proxyURL)
    default:
        http.Error(w, "Must provide ID, URL, or Address parameter", http.StatusBadRequest)
        return
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, property)
}


// Status handler to check proxy health and system status
func statusHandler(w http.ResponseWriter, r *http.Request) {
    status := map[string]interface{}{
        "status": "ok",
        "timestamp": time.Now().UTC().Format(time.RFC3339),
        "proxy_count": 0,
        "proxy_status": "disabled",
    }

    if globalProxyRotator != nil {
        status["proxy_count"] = globalProxyRotator.Count()
        if globalProxyRotator.Count() > 0 {
            status["proxy_status"] = "enabled"
        }
    }

    writeJSONResponse(w, status)
}

// Test proxy connectivity
func testProxyHandler(w http.ResponseWriter, r *http.Request) {
    results := testProxyConnectivity()
    writeJSONResponse(w, results)
}

func testProxyConnectivity() map[string]interface{} {
    results := map[string]interface{}{
        "timestamp": time.Now().UTC().Format(time.RFC3339),
        "total_proxies": 0,
        "successful_proxies": 0,
        "proxy_details": []map[string]interface{}{},
    }

    if globalProxyRotator == nil || globalProxyRotator.Count() == 0 {
        results["status"] = "no_proxies_configured"
        return results
    }

    proxyCount := globalProxyRotator.Count()
    results["total_proxies"] = proxyCount
    
    successCount := 0
    proxyDetails := []map[string]interface{}{}

    // Test each proxy
    for i := 0; i < proxyCount; i++ {
        proxyURL := globalProxyRotator.GetNext()
        proxyDetail := map[string]interface{}{
            "proxy": proxyURL.String(),
            "success": false,
            "response_time_ms": 0,
        }

        startTime := time.Now()
        client := &http.Client{
            Transport: &http.Transport{
                Proxy: http.ProxyURL(proxyURL),
            },
            Timeout: 10 * time.Second,
        }

        req, _ := http.NewRequest("GET", "http://httpbin.org/ip", nil)
        utils.SetBrowserHeaders(req, utils.NewUserAgentRotator().GetRandom())

        resp, err := client.Do(req)
        responseTime := time.Since(startTime).Milliseconds()
        proxyDetail["response_time_ms"] = responseTime

        if err != nil {
            proxyDetail["error"] = err.Error()
        } else {
            defer resp.Body.Close()
            if resp.StatusCode == 200 {
                proxyDetail["success"] = true
                successCount++
            } else {
                proxyDetail["status_code"] = resp.StatusCode
            }
        }

        proxyDetails = append(proxyDetails, proxyDetail)
    }

    results["successful_proxies"] = successCount
    results["success_rate"] = float64(successCount) / float64(proxyCount)
    results["proxy_details"] = proxyDetails
    
    return results
}

func writeJSONResponse(w http.ResponseWriter, data interface{}) {
    w.Header().Set("Content-Type", "application/json")
    // CORS headers are already set by the middleware, but ensure they're present
    if w.Header().Get("Access-Control-Allow-Origin") == "" {
        w.Header().Set("Access-Control-Allow-Origin", "*")
    }
    rawJSON, err := json.MarshalIndent(data, "", "  ")
    if err != nil {
        http.Error(w, fmt.Sprintf("Error marshalling property details: %v", err), http.StatusInternalServerError)
        return
    }
    w.Write(rawJSON)
}

// Dashboard API handlers
func searchHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var searchReq struct {
        Query string `json:"query"`
        Filters struct {
            MinPrice int `json:"minPrice"`
            MaxPrice int `json:"maxPrice"`
            PropertyType string `json:"propertyType"`
            Zoning string `json:"zoning"`
            ChainPotential string `json:"chainPotential"`
        } `json:"filters"`
    }

    if err := json.NewDecoder(r.Body).Decode(&searchReq); err != nil {
        http.Error(w, "Invalid JSON", http.StatusBadRequest)
        return
    }

    // Convert filters to map for database query
    filters := make(map[string]interface{})
    if searchReq.Filters.Zoning != "" {
        filters["zoning"] = searchReq.Filters.Zoning
    }
    if searchReq.Filters.MinPrice > 0 {
        filters["minPrice"] = searchReq.Filters.MinPrice
    }
    if searchReq.Filters.MaxPrice > 0 {
        filters["maxPrice"] = searchReq.Filters.MaxPrice
    }
    if searchReq.Filters.ChainPotential != "" {
        filters["chainPotential"] = searchReq.Filters.ChainPotential
    }

    // Get properties from database
    properties, err := database.GetProperties(filters)
    if err != nil {
        log.Printf("Error retrieving properties: %v", err)
        http.Error(w, "Error retrieving properties", http.StatusInternalServerError)
        return
    }

    // Convert to response format
    results := make([]map[string]interface{}, len(properties))
    for i, prop := range properties {
        results[i] = map[string]interface{}{
            "id":                   prop.ID,
            "address":              prop.Address,
            "price":                prop.Price,
            "size":                 prop.Size,
            "zoning":               prop.Zoning,
            "lat":                  prop.Latitude,
            "lng":                  prop.Longitude,
            "description":          prop.Description,
            "habitability":         prop.Habitability,
            "proximity":            prop.Proximity,
            "chainLeasePotential":  prop.ChainLeasePotential,
            "daysOnMarket":         prop.DaysOnMarket,
            "pricePerSqFt":         prop.PricePerSqFt,
        }
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "results": results,
        "total": len(results),
    })
}

func dashboardStatsHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Get stats from database
    stats, err := database.GetDashboardStats()
    if err != nil {
        log.Printf("Error retrieving dashboard stats: %v", err)
        http.Error(w, "Error retrieving dashboard stats", http.StatusInternalServerError)
        return
    }

    // Add recent activity (mock data for now)
    stats["recentActivity"] = []map[string]interface{}{
        {
            "type": "new_listing",
            "address": "555 Speedway Blvd, Daytona Beach, FL",
            "price": 180000,
            "timestamp": "2024-01-15T10:30:00Z",
        },
        {
            "type": "price_drop",
            "address": "777 Atlantic Ave, Daytona Beach, FL",
            "oldPrice": 220000,
            "newPrice": 195000,
            "timestamp": "2024-01-15T09:15:00Z",
        },
        {
            "type": "sold",
            "address": "999 Ridgewood Ave, Daytona Beach, FL",
            "price": 145000,
            "timestamp": "2024-01-14T16:45:00Z",
        },
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(stats)
}

// Search endpoint handler - integrates with existing search API
func searchEndpointHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Parse query parameters
    neLat, _ := strconv.ParseFloat(r.URL.Query().Get("neLat"), 64)
    neLong, _ := strconv.ParseFloat(r.URL.Query().Get("neLong"), 64)
    swLat, _ := strconv.ParseFloat(r.URL.Query().Get("swLat"), 64)
    swLong, _ := strconv.ParseFloat(r.URL.Query().Get("swLong"), 64)

    pagination, _ := strconv.Atoi(r.URL.Query().Get("pagination"))
    if pagination == 0 {
        pagination = 1
    }

    zoom, _ := strconv.Atoi(r.URL.Query().Get("zoom"))
    if zoom == 0 {
        zoom = 10
    }

    priceMin, _ := strconv.Atoi(r.URL.Query().Get("priceMin"))
    priceMax, _ := strconv.Atoi(r.URL.Query().Get("priceMax"))

    // Check for land filter
    isLotLand := r.URL.Query().Get("isLotLand") == "true"

    // Use proxy rotator
    var proxyURL *url.URL
    if globalProxyRotator != nil && globalProxyRotator.Count() > 0 {
        proxyURL = globalProxyRotator.GetNext()
    }

    // Call the existing search function for land properties
    listResults, mapResults, err := search.ForSale(
        pagination, zoom, neLat, neLong, swLat, swLong,
        false, false, false, false, false, false, false, false, // school filters
        false, false, false, isLotLand, false, false, false, // property type filters
        priceMin, priceMax, 0, 0, // price and payment filters
        proxyURL,
    )

    if err != nil {
        log.Printf("Error searching properties: %v", err)
        http.Error(w, "Error searching properties", http.StatusInternalServerError)
        return
    }

    response := map[string]interface{}{
        "listResults": listResults,
        "mapResults":  mapResults,
        "total":       len(listResults),
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(response)
}

// Sync properties handler - fetches live data and updates database
func syncPropertiesHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    log.Println("Starting property sync for Daytona Beach vacant land...")

    // Daytona Beach coordinates
    neLat := 29.3
    neLong := -80.9
    swLat := 29.1
    swLong := -81.1

    var proxyURL *url.URL
    if globalProxyRotator != nil && globalProxyRotator.Count() > 0 {
        proxyURL = globalProxyRotator.GetNext()
    }

    // Search for land properties in Daytona Beach
    listResults, _, err := search.ForSale(
        1, 12, neLat, neLong, swLat, swLong,
        false, false, false, false, false, false, false, false, // school filters
        false, false, false, true, false, false, false, // isLotLand = true
        0, 0, 0, 0, // no price filters for initial sync
        proxyURL,
    )

    if err != nil {
        log.Printf("Error fetching properties: %v", err)
        http.Error(w, "Error fetching properties", http.StatusInternalServerError)
        return
    }

    syncedCount := 0
    for _, property := range listResults {
        if err := syncPropertyToDatabase(property); err != nil {
            log.Printf("Error syncing property %s: %v", property.Address, err)
            continue
        }
        syncedCount++
    }

    response := map[string]interface{}{
        "message":      "Property sync completed",
        "totalFound":   len(listResults),
        "syncedCount":  syncedCount,
        "timestamp":    time.Now().Format(time.RFC3339),
    }

    log.Printf("Property sync completed: %d/%d properties synced", syncedCount, len(listResults))

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(response)
}

// Refresh data handler - manual trigger for data refresh
func refreshDataHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Clear existing sample data and sync fresh data
    if err := database.ClearSampleData(); err != nil {
        log.Printf("Error clearing sample data: %v", err)
    }

    // Trigger property sync
    syncPropertiesHandler(w, r)
}

// Helper function to sync a property to the database
func syncPropertyToDatabase(property search.ListResult) error {
    // Skip if no valid price
    if property.UnformattedPrice <= 0 {
        return nil
    }

    // Determine zoning based on property type and description
    zoning := determineZoning(property)

    // Calculate habitability
    habitability := determineHabitability(property)

    // Calculate proximity to beach (Daytona Beach is coastal)
    proximity := calculateProximity(property.LatLong.Latitude, property.LatLong.Longitude)

    // Calculate chain lease potential
    chainPotential := calculateChainPotential(property, zoning, proximity)

    // Calculate price per sq ft (estimate based on lot size)
    pricePerSqFt := calculatePricePerSqFt(property)

    // Calculate lot size
    lotSize := "Unknown"
    if property.HdpData.HomeInfo.LotAreaValue > 0 {
        acres := float64(property.HdpData.HomeInfo.LotAreaValue) / 43560
        if acres >= 1.0 {
            lotSize = fmt.Sprintf("%.2f acres", acres)
        } else {
            lotSize = fmt.Sprintf("%.0f sq ft", float64(property.HdpData.HomeInfo.LotAreaValue))
        }
    }

    // Insert or update property in database
    return database.UpsertProperty(database.Property{
        Address:             property.Address,
        Price:               property.UnformattedPrice,
        Size:                lotSize,
        Zoning:              zoning,
        Latitude:            property.LatLong.Latitude,
        Longitude:           property.LatLong.Longitude,
        Description:         fmt.Sprintf("%s - %s", property.HdpData.HomeInfo.HomeType, property.HdpData.HomeInfo.HomeStatus),
        Habitability:        habitability,
        Proximity:           proximity,
        ChainLeasePotential: chainPotential,
        DaysOnMarket:        property.HdpData.HomeInfo.DaysOnZillow,
        PricePerSqFt:        pricePerSqFt,
    })
}

// Determine zoning based on property characteristics
func determineZoning(property search.ListResult) string {
    propertyType := strings.ToLower(property.HdpData.HomeInfo.HomeType)
    homeStatus := strings.ToLower(property.HdpData.HomeInfo.HomeStatus)

    // Check for commercial indicators
    if strings.Contains(propertyType, "commercial") ||
       strings.Contains(homeStatus, "commercial") ||
       strings.Contains(propertyType, "lot") {
        return "Commercial"
    }

    // Check for industrial indicators
    if strings.Contains(propertyType, "industrial") ||
       strings.Contains(propertyType, "warehouse") {
        return "Industrial"
    }

    // Check for mixed use
    if strings.Contains(propertyType, "mixed") {
        return "Mixed Use"
    }

    // Default to residential for land
    return "Residential"
}

// Determine habitability status
func determineHabitability(property search.ListResult) string {
    homeStatus := strings.ToLower(property.HdpData.HomeInfo.HomeStatus)
    homeType := strings.ToLower(property.HdpData.HomeInfo.HomeType)

    if strings.Contains(homeStatus, "for sale") || strings.Contains(homeType, "lot") {
        return "Buildable"
    }

    if property.HdpData.HomeInfo.LotAreaValue > 0 {
        return "Buildable"
    }

    return "Needs Assessment"
}

// Calculate proximity to beach and city center
func calculateProximity(lat, lng float64) string {
    // Daytona Beach coordinates
    beachLat := 29.2108
    beachLng := -81.0228

    // Simple distance calculation (approximate)
    latDiff := lat - beachLat
    lngDiff := lng - beachLng
    distance := (latDiff*latDiff + lngDiff*lngDiff) * 69 // Rough miles conversion

    if distance < 0.5 {
        return "Beachfront"
    } else if distance < 1.0 {
        return "0.5 miles to beach"
    } else if distance < 2.0 {
        return fmt.Sprintf("%.1f miles to beach", distance)
    } else {
        return fmt.Sprintf("%.1f miles to city center", distance)
    }
}

// Calculate chain lease potential
func calculateChainPotential(property search.ListResult, zoning, proximity string) string {
    score := 0

    // Zoning factor
    switch zoning {
    case "Commercial":
        score += 3
    case "Mixed Use":
        score += 2
    case "Industrial":
        score += 1
    }

    // Proximity factor
    if strings.Contains(proximity, "Beachfront") {
        score += 3
    } else if strings.Contains(proximity, "0.5 miles") {
        score += 2
    } else if strings.Contains(proximity, "1.") {
        score += 1
    }

    // Size factor (larger lots better for chains)
    if property.HdpData.HomeInfo.LotAreaValue > 87120 { // > 2 acres
        score += 2
    } else if property.HdpData.HomeInfo.LotAreaValue > 43560 { // > 1 acre
        score += 1
    }

    // Price factor (reasonable for development)
    if property.UnformattedPrice < 500000 && property.UnformattedPrice > 100000 {
        score += 1
    }

    switch {
    case score >= 7:
        return "Very High"
    case score >= 5:
        return "High"
    case score >= 3:
        return "Medium"
    default:
        return "Low"
    }
}

// Calculate price per square foot
func calculatePricePerSqFt(property search.ListResult) float64 {
    if property.HdpData.HomeInfo.LotAreaValue > 0 {
        return float64(property.UnformattedPrice) / float64(property.HdpData.HomeInfo.LotAreaValue)
    }
    return 0.0
}
