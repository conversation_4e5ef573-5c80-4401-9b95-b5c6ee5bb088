#!/bin/bash

# Production Deployment Script for Propbolt Real Estate Platform
# Deploys Go backend and Next.js frontend to Google App Engine with monitoring

set -e  # Exit on any error

echo "🚀 Starting Production Deployment for Propbolt Platform"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if gcloud is installed and authenticated
print_status "Checking Google Cloud CLI setup..."
if ! command -v gcloud &> /dev/null; then
    print_error "Google Cloud CLI is not installed. Please install it first."
    exit 1
fi

# Get current project
PROJECT_ID=$(gcloud config get-value project 2>/dev/null)
if [ -z "$PROJECT_ID" ]; then
    print_error "No Google Cloud project set. Please run: gcloud config set project YOUR_PROJECT_ID"
    exit 1
fi

print_success "Using Google Cloud project: $PROJECT_ID"

# Phase 1: Backend Deployment
echo ""
echo "📦 PHASE 1: BACKEND DEPLOYMENT"
echo "=============================="

print_status "Cleaning Go dependencies..."
go mod tidy

print_status "Building Go application..."
go build -o propbolt

print_status "Setting up backend .gcloudignore..."
cp .gcloudignore-backend .gcloudignore

print_status "Deploying Go backend to default service..."
gcloud app deploy app.yaml --quiet

print_success "Backend deployed successfully!"

# Test backend
print_status "Testing backend health..."
BACKEND_URL="https://$PROJECT_ID.uc.r.appspot.com"
if curl -s "$BACKEND_URL/status" | grep -q '"status": "ok"'; then
    print_success "Backend health check passed!"
else
    print_warning "Backend health check failed, but continuing deployment..."
fi

# Phase 2: Frontend Deployment
echo ""
echo "🎨 PHASE 2: FRONTEND DEPLOYMENT"
echo "==============================="

print_status "Setting up frontend .gcloudignore..."
cp .gcloudignore-frontend .gcloudignore

print_status "Deploying Next.js frontend to frontend service..."
gcloud app deploy frontend-app.yaml --quiet

print_status "Deploying dispatch.yaml for routing..."
gcloud app deploy dispatch.yaml --quiet

print_success "Frontend deployed successfully!"

# Test frontend
print_status "Testing frontend..."
FRONTEND_URL="https://frontend-dot-$PROJECT_ID.uc.r.appspot.com"
if curl -s "$FRONTEND_URL/" | grep -q "Vacant Land Search"; then
    print_success "Frontend health check passed!"
else
    print_warning "Frontend health check failed, but continuing..."
fi

# Phase 3: Monitoring and Logging Setup
echo ""
echo "📊 PHASE 3: MONITORING & LOGGING SETUP"
echo "======================================"

print_status "Setting up uptime monitoring..."

# Create uptime checks if they don't exist
if ! gcloud monitoring uptime list-configs | grep -q "Backend API Health Check"; then
    print_status "Creating backend uptime check..."
    gcloud monitoring uptime create "Backend API Health Check" \
        --resource-type=uptime-url \
        --resource-labels=host=$PROJECT_ID.uc.r.appspot.com,project_id=$PROJECT_ID \
        --protocol=https \
        --path=/status \
        --matcher-content='"status": "ok"' \
        --matcher-type=contains-string \
        --period=1 \
        --timeout=10 \
        --regions=usa-oregon,usa-iowa,usa-virginia
    print_success "Backend uptime check created!"
else
    print_success "Backend uptime check already exists!"
fi

if ! gcloud monitoring uptime list-configs | grep -q "Frontend App Health Check"; then
    print_status "Creating frontend uptime check..."
    gcloud monitoring uptime create "Frontend App Health Check" \
        --resource-type=uptime-url \
        --resource-labels=host=frontend-dot-$PROJECT_ID.uc.r.appspot.com,project_id=$PROJECT_ID \
        --protocol=https \
        --path=/ \
        --matcher-content="Vacant Land Search" \
        --matcher-type=contains-string \
        --period=1 \
        --timeout=10 \
        --regions=usa-oregon,usa-iowa,usa-virginia
    print_success "Frontend uptime check created!"
else
    print_success "Frontend uptime check already exists!"
fi

# Enable logging APIs
print_status "Enabling Google Cloud Logging APIs..."
gcloud services enable logging.googleapis.com --quiet
gcloud services enable monitoring.googleapis.com --quiet

print_success "Monitoring and logging setup complete!"

# Phase 4: Cleanup and Summary
echo ""
echo "🧹 PHASE 4: CLEANUP & SUMMARY"
echo "============================="

print_status "Cleaning up temporary files..."
rm -f backend-uptime-config.yaml frontend-uptime-config.yaml
rm -f backend-alert-policy.yaml frontend-alert-policy.yaml

# Final status check
echo ""
echo "🎉 DEPLOYMENT COMPLETE!"
echo "======================"
echo ""
echo "📋 DEPLOYMENT SUMMARY:"
echo "----------------------"
echo "🔧 Backend API:     https://$PROJECT_ID.uc.r.appspot.com"
echo "🌐 Frontend App:    https://frontend-dot-$PROJECT_ID.uc.r.appspot.com"
echo "🔀 Custom Domains:  api.propbolt.com → Backend"
echo "                    propbolt.com → Frontend"
echo ""
echo "📊 MONITORING:"
echo "--------------"
echo "✅ Uptime checks configured for both services"
echo "✅ Google Cloud Logging enabled"
echo "✅ Auto-scaling: Backend (1-10 instances), Frontend (1-5 instances)"
echo ""
echo "🔍 USEFUL COMMANDS:"
echo "------------------"
echo "View logs:          gcloud app logs tail"
echo "View backend logs:  gcloud app logs tail -s default"
echo "View frontend logs: gcloud app logs tail -s frontend"
echo "View services:      gcloud app services list"
echo "View versions:      gcloud app versions list"
echo "View uptime checks: gcloud monitoring uptime list-configs"
echo ""
echo "🌐 ACCESS URLS:"
echo "--------------"
echo "Production Frontend: https://propbolt.com"
echo "Production API:      https://api.propbolt.com"
echo "Direct Backend:      https://$PROJECT_ID.uc.r.appspot.com"
echo "Direct Frontend:     https://frontend-dot-$PROJECT_ID.uc.r.appspot.com"
echo ""

print_success "All services deployed and monitoring configured successfully!"
print_status "You can now safely close this session. Services are running in production."
