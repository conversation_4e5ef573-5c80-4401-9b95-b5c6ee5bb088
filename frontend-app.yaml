runtime: nodejs20
service: frontend

# Environment variables for Next.js
env_variables:
  NODE_ENV: "production"
  NEXT_PUBLIC_API_BASE_URL: "https://gold-braid-458901-v2.uc.r.appspot.com"
  NEXT_PUBLIC_MAPBOX_TOKEN: "pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY202anNoNWtvMDRpMjJqb2x1MWZ3ZGYxbCJ9.Bxl18yPka0BUSrGibMTbhg"
  NEXT_PUBLIC_REAL_ESTATE_API_KEY: "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
  NEXT_PUBLIC_REAL_ESTATE_API_URL: "https://api.realestateapi.com/v2/"
  NEXT_PUBLIC_APP_NAME: "Vacant Land Search"
  NEXT_PUBLIC_APP_DESCRIPTION: "Professional real estate admin panel for vacant land search in Daytona Beach, Florida"
  PORT: "8080"
  NEXTAUTH_URL: "https://propbolt.com"
  NEXTAUTH_SECRET: "propbolt-nextauth-secret-2024-production-key-v1"

# Automatic scaling configuration
automatic_scaling:
  min_instances: 1
  max_instances: 5
  target_cpu_utilization: 0.6

# Resource allocation
resources:
  cpu: 1
  memory_gb: 1
