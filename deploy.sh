#!/bin/bash

# 🚀 PropBolt Production Deployment Script
# Deploys both backend and frontend to Google App Engine

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Configuration
PROJECT_ID="gold-braid-458901-v2"
DOMAIN="propbolt.com"

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GRE<PERSON>}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_step "Checking prerequisites..."
    
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI not found"
        exit 1
    fi
    
    if ! command -v go &> /dev/null; then
        print_error "Go not found"
        exit 1
    fi
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js not found"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm not found"
        exit 1
    fi
    
    # Check project
    CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null)
    if [ "$CURRENT_PROJECT" != "$PROJECT_ID" ]; then
        print_warning "Setting project to $PROJECT_ID"
        gcloud config set project $PROJECT_ID
    fi
    
    print_success "Prerequisites verified"
}

# Deploy backend
deploy_backend() {
    print_header "DEPLOYING BACKEND"

    print_step "Cleaning Go dependencies..."
    go mod tidy

    print_step "Building Go application..."
    go build -o propbolt

    if [ ! -f "propbolt" ]; then
        print_error "Go build failed"
        exit 1
    fi

    print_step "Using backend-specific .gcloudignore..."
    # Backup original .gcloudignore and use backend-specific one
    if [ -f ".gcloudignore" ]; then
        cp .gcloudignore .gcloudignore.backup
    fi
    cp .gcloudignore-backend .gcloudignore

    print_step "Deploying backend to App Engine..."
    gcloud app deploy app.yaml --quiet

    # Restore original .gcloudignore
    if [ -f ".gcloudignore.backup" ]; then
        mv .gcloudignore.backup .gcloudignore
    fi

    print_success "Backend deployed successfully"
}

# Deploy frontend
deploy_frontend() {
    print_header "DEPLOYING FRONTEND"

    print_step "Installing Node.js dependencies..."
    npm ci --silent

    print_step "Running type checking..."
    npm run type-check

    print_step "Building Next.js application..."
    npm run build

    if [ ! -d ".next" ]; then
        print_error "Next.js build failed"
        exit 1
    fi

    print_step "Cleaning up unnecessary files before deployment..."

    # Note: We rely on .gcloudignore to exclude node_modules during upload
    # Don't remove node_modules here as it may be needed for the build process

    # Clean up .next cache and development files
    if [ -d ".next/cache" ]; then
        print_step "Cleaning .next cache..."
        rm -rf .next/cache
    fi

    # Remove other unnecessary files that might cause file count issues
    print_step "Removing other unnecessary files..."
    rm -f .next/trace 2>/dev/null || true
    rm -f .next/build-manifest.json 2>/dev/null || true
    rm -f .next/export-marker.json 2>/dev/null || true
    rm -f .next/prerender-manifest.json 2>/dev/null || true
    rm -f .next/routes-manifest.json 2>/dev/null || true
    rm -f .next/images-manifest.json 2>/dev/null || true

    # Verify essential .next files exist
    if [ ! -d ".next/static" ] || [ ! -d ".next/server" ]; then
        print_error "Essential Next.js build files missing (.next/static or .next/server)"
        exit 1
    fi

    print_step "Counting files to be deployed..."
    FILE_COUNT=$(find . -type f \
        ! -path "./node_modules/*" \
        ! -path "./.git/*" \
        ! -path "./.next/cache/*" \
        ! -name "*.md" \
        ! -name "*.sh" \
        ! -name "*.go" \
        ! -name "go.mod" \
        ! -name "go.sum" \
        ! -name "propbolt" \
        ! -name "package-lock.json" \
        ! -name ".gitignore" \
        ! -name "tsconfig.json" \
        ! -name "next.config.js" \
        ! -name "tailwind.config.js" \
        ! -name "postcss.config.js" \
        ! -name "next-env.d.ts" \
        | wc -l)

    print_step "Estimated files to deploy: $FILE_COUNT"

    if [ "$FILE_COUNT" -gt 9000 ]; then
        print_warning "File count ($FILE_COUNT) is approaching the 10,000 limit"
    fi

    print_step "Using frontend-specific .gcloudignore..."
    # Backup original .gcloudignore and use frontend-specific one
    if [ -f ".gcloudignore" ]; then
        cp .gcloudignore .gcloudignore.backup
    fi
    cp .gcloudignore-frontend .gcloudignore

    # Final file count verification with .gcloudignore applied
    print_step "Final verification of files to be deployed..."

    # Create a temporary directory to simulate what gcloud will upload
    TEMP_DIR=$(mktemp -d)
    rsync -av --exclude-from=.gcloudignore . "$TEMP_DIR/" > /dev/null 2>&1
    FINAL_FILE_COUNT=$(find "$TEMP_DIR" -type f | wc -l)
    rm -rf "$TEMP_DIR"

    print_step "Final file count after .gcloudignore: $FINAL_FILE_COUNT"

    if [ "$FINAL_FILE_COUNT" -gt 10000 ]; then
        print_error "File count ($FINAL_FILE_COUNT) exceeds App Engine limit of 10,000 files"
        # Restore original .gcloudignore before exiting
        if [ -f ".gcloudignore.backup" ]; then
            mv .gcloudignore.backup .gcloudignore
        fi
        exit 1
    elif [ "$FINAL_FILE_COUNT" -gt 8000 ]; then
        print_warning "File count ($FINAL_FILE_COUNT) is high but within limits"
    else
        print_success "File count ($FINAL_FILE_COUNT) is well within limits"
    fi

    print_step "Deploying frontend to App Engine..."
    gcloud app deploy frontend-app.yaml --quiet

    # Restore original .gcloudignore
    if [ -f ".gcloudignore.backup" ]; then
        mv .gcloudignore.backup .gcloudignore
    fi

    print_success "Frontend deployed successfully"
}

# Setup routing
setup_routing() {
    print_header "CONFIGURING ROUTING"

    print_step "Using existing dispatch configuration..."
    if [ ! -f "dispatch.yaml" ]; then
        print_error "dispatch.yaml not found. Creating default configuration..."
        cat > dispatch.yaml << EOF
dispatch:
  # Route api.propbolt.com to backend (default service)
  - url: "api.propbolt.com/*"
    service: default

  # Route propbolt.com and www.propbolt.com to frontend service
  - url: "propbolt.com/*"
    service: frontend

  - url: "www.propbolt.com/*"
    service: frontend

  # Default routing for App Engine URLs
  - url: "*/api/*"
    service: default

  - url: "*/"
    service: frontend
EOF
    else
        print_step "Found existing dispatch.yaml configuration"
    fi

    print_step "Deploying routing configuration..."
    gcloud app deploy dispatch.yaml --quiet

    print_success "Routing configured"
}

# Verify deployment
verify_deployment() {
    print_header "VERIFYING DEPLOYMENT"

    print_step "Testing backend API at api.propbolt.com..."
    if curl -s "https://api.propbolt.com/status" | grep -q "ok"; then
        print_success "Backend API is responding at api.propbolt.com"
    else
        print_warning "Backend API test failed at api.propbolt.com"
    fi

    print_step "Testing frontend at propbolt.com..."
    if curl -s "https://propbolt.com/" | grep -q "html\|DOCTYPE"; then
        print_success "Frontend is responding at propbolt.com"
    else
        print_warning "Frontend test failed at propbolt.com"
    fi

    print_step "Testing cross-service connectivity..."
    print_step "Checking if frontend can reach backend API..."

    print_step "Checking services..."
    gcloud app services list

    print_success "Deployment verification complete"
}

# Main deployment flow
main() {
    print_header "PROPBOLT PRODUCTION DEPLOYMENT"
    echo -e "${BLUE}Domain: ${NC}$DOMAIN"
    echo -e "${BLUE}Project: ${NC}$PROJECT_ID"
    echo ""
    
    check_prerequisites
    deploy_backend
    deploy_frontend
    setup_routing
    verify_deployment
    
    print_header "DEPLOYMENT COMPLETE"
    echo ""
    echo -e "${GREEN}🎉 PropBolt is now live with separated services:${NC}"
    echo ""
    echo -e "${BLUE}📊 Services:${NC}"
    echo -e "${BLUE}   Frontend: https://propbolt.com${NC}"
    echo -e "${BLUE}   Backend API: https://api.propbolt.com${NC}"
    echo -e "${BLUE}   API Status: https://api.propbolt.com/status${NC}"
    echo ""
    echo -e "${GREEN}🔗 Service Architecture:${NC}"
    echo -e "${GREEN}   • Frontend (Next.js): propbolt.com → 'frontend' service${NC}"
    echo -e "${GREEN}   • Backend (Go API): api.propbolt.com → 'default' service${NC}"
    echo -e "${GREEN}   • Cross-origin requests properly configured${NC}"
    echo ""
    echo -e "${YELLOW}📋 Management Commands:${NC}"
    echo -e "${YELLOW}   gcloud app services list${NC}"
    echo -e "${YELLOW}   gcloud app versions list${NC}"
    echo -e "${YELLOW}   gcloud app logs tail -s default    # Backend logs${NC}"
    echo -e "${YELLOW}   gcloud app logs tail -s frontend   # Frontend logs${NC}"
    echo ""
}

# Run deployment
main "$@"
