#!/bin/bash

# Google Cloud Logging Setup Script
# Sets up comprehensive logging for the Propbolt platform

set -e

echo "📊 Setting up Google Cloud Logging for Propbolt Platform"
echo "========================================================"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

PROJECT_ID=$(gcloud config get-value project 2>/dev/null)

print_status "Enabling required APIs..."
gcloud services enable logging.googleapis.com --quiet
gcloud services enable monitoring.googleapis.com --quiet
gcloud services enable clouderrorreporting.googleapis.com --quiet

print_status "Setting up log-based metrics..."

# Create log-based metric for backend errors
gcloud logging metrics create backend_errors \
    --description="Backend API error count" \
    --log-filter='resource.type="gae_app" AND resource.labels.module_id="default" AND severity>=ERROR' \
    --quiet || echo "Backend error metric already exists"

# Create log-based metric for frontend errors  
gcloud logging metrics create frontend_errors \
    --description="Frontend app error count" \
    --log-filter='resource.type="gae_app" AND resource.labels.module_id="frontend" AND severity>=ERROR' \
    --quiet || echo "Frontend error metric already exists"

print_status "Setting up log retention..."
# App Engine logs are automatically retained for 30 days by default

print_success "Google Cloud Logging setup complete!"

echo ""
echo "📋 LOGGING COMMANDS:"
echo "==================="
echo "View all logs:        gcloud app logs tail"
echo "Backend logs only:    gcloud app logs tail -s default"
echo "Frontend logs only:   gcloud app logs tail -s frontend"
echo "Error logs only:      gcloud app logs tail --level=error"
echo "Live log streaming:   gcloud app logs tail --follow"
echo ""
echo "🔍 LOG FILTERS:"
echo "==============="
echo "Backend API calls:    resource.type=\"gae_app\" AND resource.labels.module_id=\"default\""
echo "Frontend requests:    resource.type=\"gae_app\" AND resource.labels.module_id=\"frontend\""
echo "Error logs:           severity>=ERROR"
echo "Proxy issues:         textPayload:\"proxy\""
echo ""
echo "📊 MONITORING URLS:"
echo "=================="
echo "Cloud Console Logs:  https://console.cloud.google.com/logs/query"
echo "App Engine Logs:     https://console.cloud.google.com/appengine/services"
echo "Monitoring:          https://console.cloud.google.com/monitoring"
echo "Uptime Checks:       https://console.cloud.google.com/monitoring/uptime"
