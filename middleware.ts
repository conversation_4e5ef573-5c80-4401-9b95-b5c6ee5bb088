// Temporarily disabled middleware for debugging
// import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';

export default function middleware() {
  // Temporarily allow all requests to debug client-side error
  return NextResponse.next();
}

// export default withAuth(
//   function middleware(req) {
//     // Allow access to auth pages without authentication
//     if (req.nextUrl.pathname.startsWith('/auth/')) {
//       return NextResponse.next();
//     }

//     // Redirect to sign-in if not authenticated
//     if (!req.nextauth.token) {
//       return NextResponse.redirect(new URL('/auth/signin', req.url));
//     }

//     return NextResponse.next();
//   },
//   {
//     callbacks: {
//       authorized: ({ token, req }) => {
//         // Allow access to auth pages
//         if (req.nextUrl.pathname.startsWith('/auth/')) {
//           return true;
//         }

//         // Require authentication for all other pages
//         return !!token;
//       },
//     },
//   }
// );

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (all API routes including NextAuth)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
};
